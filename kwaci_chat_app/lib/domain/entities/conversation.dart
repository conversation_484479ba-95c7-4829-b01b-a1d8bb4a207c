import 'package:equatable/equatable.dart';

/// Represents a conversation from the API
class Conversation extends Equatable {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int messageCount;
  final ConversationLastMessage? lastMessage;

  const Conversation({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.messageCount,
    this.lastMessage,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        createdAt,
        updatedAt,
        messageCount,
        lastMessage,
      ];

  Conversation copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? messageCount,
    ConversationLastMessage? lastMessage,
  }) {
    return Conversation(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messageCount: messageCount ?? this.messageCount,
      lastMessage: lastMessage ?? this.lastMessage,
    );
  }

  /// Get display title for the conversation
  String get displayTitle {
    if (title.isNotEmpty) return title;
    if (lastMessage != null && lastMessage!.content.isNotEmpty) {
      return lastMessage!.content.length > 50
          ? '${lastMessage!.content.substring(0, 50)}...'
          : lastMessage!.content;
    }
    return 'New Conversation';
  }
}

/// Represents the last message in a conversation
class ConversationLastMessage extends Equatable {
  final String content;
  final DateTime timestamp;
  final bool isUser;

  const ConversationLastMessage({
    required this.content,
    required this.timestamp,
    required this.isUser,
  });

  @override
  List<Object?> get props => [content, timestamp, isUser];

  ConversationLastMessage copyWith({
    String? content,
    DateTime? timestamp,
    bool? isUser,
  }) {
    return ConversationLastMessage(
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isUser: isUser ?? this.isUser,
    );
  }
}

/// Response wrapper for conversations API
class ConversationsResponse extends Equatable {
  final List<Conversation> conversations;
  final int total;
  final int limit;
  final int offset;

  const ConversationsResponse({
    required this.conversations,
    required this.total,
    required this.limit,
    required this.offset,
  });

  @override
  List<Object?> get props => [conversations, total, limit, offset];

  ConversationsResponse copyWith({
    List<Conversation>? conversations,
    int? total,
    int? limit,
    int? offset,
  }) {
    return ConversationsResponse(
      conversations: conversations ?? this.conversations,
      total: total ?? this.total,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Check if there are more conversations to load
  bool get hasMore => offset + conversations.length < total;
}
