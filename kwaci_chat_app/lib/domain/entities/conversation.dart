import 'package:equatable/equatable.dart';

/// Represents a conversation from the API
class Conversation extends Equatable {
  final String id;
  final String userId;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int messageCount;
  final String? lastMessagePreview;

  const Conversation({
    required this.id,
    required this.userId,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.messageCount,
    this.lastMessagePreview,
  });

  @override
  List<Object?> get props => [
    id,
    userId,
    title,
    createdAt,
    updatedAt,
    messageCount,
    lastMessagePreview,
  ];

  Conversation copyWith({
    String? id,
    String? userId,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? messageCount,
    String? lastMessagePreview,
  }) {
    return Conversation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messageCount: messageCount ?? this.messageCount,
      lastMessagePreview: lastMessagePreview ?? this.lastMessagePreview,
    );
  }

  /// Get display title for the conversation
  String get displayTitle {
    if (title.isNotEmpty) return title;
    if (lastMessagePreview != null && lastMessagePreview!.isNotEmpty) {
      return lastMessagePreview!.length > 50
          ? '${lastMessagePreview!.substring(0, 50)}...'
          : lastMessagePreview!;
    }
    return 'New Conversation';
  }
}

/// Response wrapper for conversations API
class ConversationsResponse extends Equatable {
  final List<Conversation> conversations;
  final int total;
  final int limit;
  final int offset;

  const ConversationsResponse({
    required this.conversations,
    required this.total,
    required this.limit,
    required this.offset,
  });

  @override
  List<Object?> get props => [conversations, total, limit, offset];

  ConversationsResponse copyWith({
    List<Conversation>? conversations,
    int? total,
    int? limit,
    int? offset,
  }) {
    return ConversationsResponse(
      conversations: conversations ?? this.conversations,
      total: total ?? this.total,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Check if there are more conversations to load
  bool get hasMore => offset + conversations.length < total;
}
