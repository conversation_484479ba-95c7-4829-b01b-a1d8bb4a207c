import 'package:equatable/equatable.dart';

/// Represents a message from the API (different from local Message entity)
class ApiMessage extends Equatable {
  final String id;
  final String conversationId;
  final String userId;
  final SenderRole senderRole;
  final MessageType messageType;
  final String content;
  final DateTime createdAt;
  final String? referenceId; // For document references

  const ApiMessage({
    required this.id,
    required this.conversationId,
    required this.userId,
    required this.senderRole,
    required this.messageType,
    required this.content,
    required this.createdAt,
    this.referenceId,
  });

  @override
  List<Object?> get props => [
        id,
        conversationId,
        userId,
        senderRole,
        messageType,
        content,
        createdAt,
        referenceId,
      ];

  ApiMessage copyWith({
    String? id,
    String? conversationId,
    String? userId,
    SenderRole? senderRole,
    MessageType? messageType,
    String? content,
    DateTime? createdAt,
    String? referenceId,
  }) {
    return ApiMessage(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      userId: userId ?? this.userId,
      senderRole: senderRole ?? this.senderRole,
      messageType: messageType ?? this.messageType,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      referenceId: referenceId ?? this.referenceId,
    );
  }

  /// Check if this message is from the user
  bool get isUser => senderRole == SenderRole.user;

  /// Check if this message is from the AI
  bool get isAi => senderRole == SenderRole.ai;
}

/// Sender role for API messages
enum SenderRole {
  user,
  ai,
}

extension SenderRoleExtension on SenderRole {
  String get value {
    switch (this) {
      case SenderRole.user:
        return 'user';
      case SenderRole.ai:
        return 'ai';
    }
  }

  static SenderRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'user':
        return SenderRole.user;
      case 'ai':
      case 'assistant':
        return SenderRole.ai;
      default:
        return SenderRole.user;
    }
  }
}

/// Message type for API messages
enum MessageType {
  text,
  document,
  system,
}

extension MessageTypeExtension on MessageType {
  String get value {
    switch (this) {
      case MessageType.text:
        return 'text';
      case MessageType.document:
        return 'document';
      case MessageType.system:
        return 'system';
    }
  }

  static MessageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'text':
        return MessageType.text;
      case 'document':
        return MessageType.document;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.text;
    }
  }
}

/// Response wrapper for messages API
class ApiMessagesResponse extends Equatable {
  final List<ApiMessage> messages;
  final int totalCount;
  final int limit;
  final int offset;

  const ApiMessagesResponse({
    required this.messages,
    required this.totalCount,
    required this.limit,
    required this.offset,
  });

  @override
  List<Object?> get props => [messages, totalCount, limit, offset];

  ApiMessagesResponse copyWith({
    List<ApiMessage>? messages,
    int? totalCount,
    int? limit,
    int? offset,
  }) {
    return ApiMessagesResponse(
      messages: messages ?? this.messages,
      totalCount: totalCount ?? this.totalCount,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Check if there are more messages to load
  bool get hasMore => offset + messages.length < totalCount;
}

/// Request for creating a new conversation
class CreateConversationRequest extends Equatable {
  final String? title;
  final String initialMessage;

  const CreateConversationRequest({
    this.title,
    required this.initialMessage,
  });

  @override
  List<Object?> get props => [title, initialMessage];

  Map<String, dynamic> toJson() {
    return {
      if (title != null) 'title': title,
      'initial_message': initialMessage,
    };
  }
}

/// Request for sending a message
class SendMessageRequest extends Equatable {
  final String content;
  final MessageType messageType;
  final String? referenceId;

  const SendMessageRequest({
    required this.content,
    this.messageType = MessageType.text,
    this.referenceId,
  });

  @override
  List<Object?> get props => [content, messageType, referenceId];

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'message_type': messageType.value,
      if (referenceId != null) 'reference_id': referenceId,
    };
  }
}

/// Response for sending a message (contains both user and AI messages)
class SendMessageResponse extends Equatable {
  final ApiMessage userMessage;
  final ApiMessage aiMessage;

  const SendMessageResponse({
    required this.userMessage,
    required this.aiMessage,
  });

  @override
  List<Object?> get props => [userMessage, aiMessage];
}
