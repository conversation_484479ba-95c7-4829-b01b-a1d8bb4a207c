import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/api_message.dart';
import '../repositories/chat_repository.dart';

class GetConversationMessages {
  final ChatRepository repository;

  GetConversationMessages(this.repository);

  Future<Either<Failure, ApiMessagesResponse>> call({
    required String conversationId,
    int limit = 50,
    int offset = 0,
  }) async {
    return await repository.fetchConversationMessages(
      conversationId: conversationId,
      limit: limit,
      offset: offset,
    );
  }
}
