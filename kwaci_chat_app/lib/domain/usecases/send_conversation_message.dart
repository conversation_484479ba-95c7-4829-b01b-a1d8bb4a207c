import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/api_message.dart';
import '../repositories/chat_repository.dart';

class SendConversationMessage {
  final ChatRepository repository;

  SendConversationMessage(this.repository);

  Future<Either<Failure, SendMessageResponse>> call({
    required String conversationId,
    required String content,
    MessageType messageType = MessageType.text,
    String? referenceId,
  }) async {
    final request = SendMessageRequest(
      content: content,
      messageType: messageType,
      referenceId: referenceId,
    );
    
    return await repository.sendConversationMessage(conversationId, request);
  }
}
