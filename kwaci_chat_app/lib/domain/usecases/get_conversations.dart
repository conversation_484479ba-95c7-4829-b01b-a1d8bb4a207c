import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/conversation.dart';
import '../repositories/chat_repository.dart';

class GetConversations {
  final ChatRepository repository;

  GetConversations(this.repository);

  Future<Either<Failure, ConversationsResponse>> call({
    int limit = 50,
    int offset = 0,
  }) async {
    return await repository.fetchConversations(
      limit: limit,
      offset: offset,
    );
  }
}
