import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/conversation.dart';
import '../entities/api_message.dart';
import '../repositories/chat_repository.dart';

class CreateConversation {
  final ChatRepository repository;

  CreateConversation(this.repository);

  Future<Either<Failure, Conversation>> call({
    String? title,
    required String initialMessage,
  }) async {
    final request = CreateConversationRequest(
      title: title,
      initialMessage: initialMessage,
    );
    
    return await repository.createConversation(request);
  }
}
