import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/message_model.dart';
import '../../models/conversation_model.dart';
import '../../models/api_message_model.dart';
import '../../../domain/entities/api_message.dart';

abstract class ChatApiService {
  Future<QueryResponse> sendQuery(QueryRequest request);
  Future<Map<String, dynamic>> healthCheck();
  Future<ConversationsResponseModel> fetchConversations({
    int limit = 50,
    int offset = 0,
  });

  // New API message endpoints
  Future<ApiMessagesResponseModel> fetchMessages(
    String conversationId, {
    int limit = 50,
    int offset = 0,
  });

  Future<ConversationModel> createConversation(
    CreateConversationRequest request,
  );

  Future<SendMessageResponseModel> sendMessage(
    String conversationId,
    SendMessageRequest request,
  );
}

class ChatApiServiceImpl implements ChatApiService {
  final DioClient _dioClient;

  ChatApiServiceImpl(this._dioClient);

  @override
  Future<QueryResponse> sendQuery(QueryRequest request) async {
    try {
      final response = await _dioClient.post('/query', data: request.toJson());
      return QueryResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await _dioClient.get('/health');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<ConversationsResponseModel> fetchConversations({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _dioClient.get(
        '/chat/conversations',
        queryParameters: {'limit': limit, 'offset': offset},
      );
      return ConversationsResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<ApiMessagesResponseModel> fetchMessages(
    String conversationId, {
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _dioClient.get(
        '/chat/conversations/$conversationId/messages',
        queryParameters: {'limit': limit, 'offset': offset},
      );
      return ApiMessagesResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<ConversationModel> createConversation(
    CreateConversationRequest request,
  ) async {
    try {
      final response = await _dioClient.post(
        '/chat/conversations',
        data: request.toJson(),
      );
      return ConversationModel.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<SendMessageResponseModel> sendMessage(
    String conversationId,
    SendMessageRequest request,
  ) async {
    try {
      final response = await _dioClient.post(
        '/chat/conversations/$conversationId/messages',
        data: request.toJson(),
      );
      return SendMessageResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['error'] ?? 'Server error occurred';
        return ServerException(message: message, code: statusCode?.toString());
      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled');
      case DioExceptionType.connectionError:
        return const NetworkException(
          message:
              'No internet connection. Please check your network settings.',
        );
      default:
        return NetworkException(
          message: error.message ?? 'Unknown network error occurred',
        );
    }
  }
}

// Provider for ChatApiService
final chatApiServiceProvider = Provider<ChatApiService>((ref) {
  final dioClient = ref.read(dioClientProvider);
  return ChatApiServiceImpl(dioClient);
});
