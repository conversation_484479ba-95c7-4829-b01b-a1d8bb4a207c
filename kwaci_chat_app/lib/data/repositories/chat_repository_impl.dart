import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/entities/chat.dart';
import '../../domain/entities/message.dart';
import '../../domain/entities/conversation.dart';
import '../../domain/repositories/chat_repository.dart';
import '../datasources/local/chat_local_data_source.dart';
import '../datasources/remote/chat_api_service.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';

class ChatRepositoryImpl implements ChatRepository {
  final ChatApiService apiService;
  final ChatLocalDataSource localDataSource;
  final Uuid _uuid = const Uuid();

  ChatRepositoryImpl({required this.apiService, required this.localDataSource});

  @override
  Future<Either<Failure, Message>> sendMessage({
    required String chatId,
    required String content,
    List<String> documentIds = const [],
  }) async {
    try {
      // Create user message
      final userMessage = MessageModel(
        id: _uuid.v4(),
        content: content,
        isUser: true,
        timestamp: DateTime.now(),
        documentReferences: documentIds,
        status: MessageStatus.sent,
      );

      // Get or create chat
      var chat = await localDataSource.getCachedChat(chatId);
      if (chat == null) {
        chat = ChatModel(
          id: chatId,
          title: '',
          messages: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      // Add user message to chat
      chat = chat.addMessage(userMessage) as ChatModel;
      await localDataSource.cacheChat(chat);

      // Send query to API
      final queryRequest = QueryRequest(
        question: content,
        documentIds: documentIds,
      );

      final queryResponse = await apiService.sendQuery(queryRequest);

      // Create AI response message with citations
      final citations =
          queryResponse.contentBlocks
              .expand((block) => block.citations)
              .map((citation) => citation.toEntity())
              .toList();

      final aiMessage = MessageModel(
        id: _uuid.v4(),
        content: queryResponse.answer,
        isUser: false,
        timestamp: DateTime.now(),
        documentReferences:
            queryResponse.sources.map((source) => source.documentId).toList(),
        citations: citations,
        status: MessageStatus.sent,
      );

      // Add AI message to chat
      chat = chat.addMessage(aiMessage) as ChatModel;

      // Update chat title if it's empty and this is the first user message
      if (chat.title.isEmpty &&
          chat.messages.where((m) => m.isUser).length == 1) {
        final title =
            content.length > 50 ? '${content.substring(0, 50)}...' : content;
        chat = ChatModel(
          id: chat.id,
          title: title,
          messages: chat.messages,
          createdAt: chat.createdAt,
          updatedAt: chat.updatedAt,
        );
      }

      await localDataSource.cacheChat(chat);

      return Right(aiMessage.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to send message: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Chat>>> getChats() async {
    try {
      // For now, we'll use local storage as the primary source
      // In a real app, you might sync with a server
      final cachedChats = await localDataSource.getCachedChats();
      return Right(cachedChats.map((chat) => chat.toEntity()).toList());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get chats: $e'));
    }
  }

  @override
  Future<Either<Failure, Chat>> getChat(String chatId) async {
    try {
      final cachedChat = await localDataSource.getCachedChat(chatId);
      if (cachedChat != null) {
        return Right(cachedChat.toEntity());
      } else {
        return const Left(CacheFailure(message: 'Chat not found'));
      }
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get chat: $e'));
    }
  }

  @override
  Future<Either<Failure, Chat>> createChat({String? title}) async {
    try {
      final chat = ChatModel(
        id: _uuid.v4(),
        title: title ?? '',
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await localDataSource.cacheChat(chat);
      return Right(chat.toEntity());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to create chat: $e'));
    }
  }

  @override
  Future<Either<Failure, Chat>> updateChatTitle(
    String chatId,
    String title,
  ) async {
    try {
      final cachedChat = await localDataSource.getCachedChat(chatId);
      if (cachedChat == null) {
        return const Left(CacheFailure(message: 'Chat not found'));
      }

      final updatedChat = ChatModel(
        id: cachedChat.id,
        title: title,
        messages: cachedChat.messages,
        createdAt: cachedChat.createdAt,
        updatedAt: DateTime.now(),
      );

      await localDataSource.cacheChat(updatedChat);
      return Right(updatedChat.toEntity());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to update chat title: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteChat(String chatId) async {
    try {
      await localDataSource.removeChat(chatId);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to delete chat: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Chat>>> getCachedChats() async {
    try {
      final cachedChats = await localDataSource.getCachedChats();
      return Right(cachedChats.map((chat) => chat.toEntity()).toList());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get cached chats: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheChats(List<Chat> chats) async {
    try {
      final chatModels =
          chats.map((chat) => ChatModel.fromEntity(chat)).toList();
      await localDataSource.cacheChats(chatModels);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to cache chats: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheChat(Chat chat) async {
    try {
      final chatModel = ChatModel.fromEntity(chat);
      await localDataSource.cacheChat(chatModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to cache chat: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearChatCache() async {
    try {
      await localDataSource.clearCache();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to clear chat cache: $e'));
    }
  }

  @override
  Future<Either<Failure, ConversationsResponse>> fetchConversations({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await apiService.fetchConversations(
        limit: limit,
        offset: offset,
      );
      return Right(response.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to fetch conversations: $e'));
    }
  }
}

// Provider for ChatRepository
final chatRepositoryProvider = Provider<ChatRepository>((ref) {
  final apiService = ref.read(chatApiServiceProvider);
  final localDataSource = ref.read(chatLocalDataSourceProvider);

  return ChatRepositoryImpl(
    apiService: apiService,
    localDataSource: localDataSource,
  );
});
