import '../../domain/entities/conversation.dart';
import '../../core/utils/date_formatter.dart';

class ConversationModel extends Conversation {
  const ConversationModel({
    required super.id,
    required super.userId,
    required super.title,
    required super.createdAt,
    required super.updatedAt,
    required super.messageCount,
    super.lastMessagePreview,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String? ?? '',
      createdAt:
          DateFormatter.parseIsoDate(json['created_at'] as String) ??
          DateTime.now(),
      updatedAt:
          DateFormatter.parseIsoDate(json['updated_at'] as String) ??
          DateTime.now(),
      messageCount: json['message_count'] as int? ?? 0,
      lastMessagePreview: json['last_message_preview'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'created_at': DateFormatter.toIsoString(createdAt),
      'updated_at': DateFormatter.toIsoString(updatedAt),
      'message_count': messageCount,
      'last_message_preview': lastMessagePreview,
    };
  }

  factory ConversationModel.fromEntity(Conversation conversation) {
    return ConversationModel(
      id: conversation.id,
      userId: conversation.userId,
      title: conversation.title,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      messageCount: conversation.messageCount,
      lastMessagePreview: conversation.lastMessagePreview,
    );
  }

  Conversation toEntity() {
    return Conversation(
      id: id,
      userId: userId,
      title: title,
      createdAt: createdAt,
      updatedAt: updatedAt,
      messageCount: messageCount,
      lastMessagePreview: lastMessagePreview,
    );
  }
}

class ConversationsResponseModel extends ConversationsResponse {
  const ConversationsResponseModel({
    required super.conversations,
    required super.total,
    required super.limit,
    required super.offset,
  });

  factory ConversationsResponseModel.fromJson(Map<String, dynamic> json) {
    return ConversationsResponseModel(
      conversations:
          (json['conversations'] as List<dynamic>?)
              ?.map(
                (conversation) => ConversationModel.fromJson(
                  conversation as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      total: json['total_count'] as int? ?? json['total'] as int? ?? 0,
      limit: json['limit'] as int? ?? 50,
      offset: json['offset'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'conversations':
          conversations
              .map(
                (conversation) => (conversation as ConversationModel).toJson(),
              )
              .toList(),
      'total': total,
      'limit': limit,
      'offset': offset,
    };
  }

  factory ConversationsResponseModel.fromEntity(
    ConversationsResponse response,
  ) {
    return ConversationsResponseModel(
      conversations:
          response.conversations
              .map((conversation) => ConversationModel.fromEntity(conversation))
              .toList(),
      total: response.total,
      limit: response.limit,
      offset: response.offset,
    );
  }

  ConversationsResponse toEntity() {
    return ConversationsResponse(
      conversations: conversations,
      total: total,
      limit: limit,
      offset: offset,
    );
  }
}
