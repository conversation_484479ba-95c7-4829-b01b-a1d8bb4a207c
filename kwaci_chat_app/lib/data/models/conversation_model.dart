import '../../domain/entities/conversation.dart';
import '../../core/utils/date_formatter.dart';

class ConversationModel extends Conversation {
  const ConversationModel({
    required super.id,
    required super.title,
    required super.createdAt,
    required super.updatedAt,
    required super.messageCount,
    super.lastMessage,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      title: json['title'] as String? ?? '',
      createdAt: DateFormatter.parseIsoDate(json['created_at'] as String) ?? DateTime.now(),
      updatedAt: DateFormatter.parseIsoDate(json['updated_at'] as String) ?? DateTime.now(),
      messageCount: json['message_count'] as int? ?? 0,
      lastMessage: json['last_message'] != null
          ? ConversationLastMessageModel.fromJson(json['last_message'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'created_at': DateFormatter.toIsoString(createdAt),
      'updated_at': DateFormatter.toIsoString(updatedAt),
      'message_count': messageCount,
      'last_message': lastMessage != null
          ? (lastMessage as ConversationLastMessageModel).toJson()
          : null,
    };
  }

  factory ConversationModel.fromEntity(Conversation conversation) {
    return ConversationModel(
      id: conversation.id,
      title: conversation.title,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      messageCount: conversation.messageCount,
      lastMessage: conversation.lastMessage != null
          ? ConversationLastMessageModel.fromEntity(conversation.lastMessage!)
          : null,
    );
  }

  Conversation toEntity() {
    return Conversation(
      id: id,
      title: title,
      createdAt: createdAt,
      updatedAt: updatedAt,
      messageCount: messageCount,
      lastMessage: lastMessage,
    );
  }
}

class ConversationLastMessageModel extends ConversationLastMessage {
  const ConversationLastMessageModel({
    required super.content,
    required super.timestamp,
    required super.isUser,
  });

  factory ConversationLastMessageModel.fromJson(Map<String, dynamic> json) {
    return ConversationLastMessageModel(
      content: json['content'] as String? ?? '',
      timestamp: DateFormatter.parseIsoDate(json['timestamp'] as String) ?? DateTime.now(),
      isUser: json['is_user'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'timestamp': DateFormatter.toIsoString(timestamp),
      'is_user': isUser,
    };
  }

  factory ConversationLastMessageModel.fromEntity(ConversationLastMessage lastMessage) {
    return ConversationLastMessageModel(
      content: lastMessage.content,
      timestamp: lastMessage.timestamp,
      isUser: lastMessage.isUser,
    );
  }

  ConversationLastMessage toEntity() {
    return ConversationLastMessage(
      content: content,
      timestamp: timestamp,
      isUser: isUser,
    );
  }
}

class ConversationsResponseModel extends ConversationsResponse {
  const ConversationsResponseModel({
    required super.conversations,
    required super.total,
    required super.limit,
    required super.offset,
  });

  factory ConversationsResponseModel.fromJson(Map<String, dynamic> json) {
    return ConversationsResponseModel(
      conversations: (json['conversations'] as List<dynamic>?)
              ?.map((conversation) => ConversationModel.fromJson(conversation as Map<String, dynamic>))
              .toList() ??
          [],
      total: json['total'] as int? ?? 0,
      limit: json['limit'] as int? ?? 50,
      offset: json['offset'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'conversations': conversations
          .map((conversation) => (conversation as ConversationModel).toJson())
          .toList(),
      'total': total,
      'limit': limit,
      'offset': offset,
    };
  }

  factory ConversationsResponseModel.fromEntity(ConversationsResponse response) {
    return ConversationsResponseModel(
      conversations: response.conversations
          .map((conversation) => ConversationModel.fromEntity(conversation))
          .toList(),
      total: response.total,
      limit: response.limit,
      offset: response.offset,
    );
  }

  ConversationsResponse toEntity() {
    return ConversationsResponse(
      conversations: conversations,
      total: total,
      limit: limit,
      offset: offset,
    );
  }
}
