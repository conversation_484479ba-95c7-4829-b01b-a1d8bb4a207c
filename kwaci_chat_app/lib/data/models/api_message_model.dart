import '../../domain/entities/api_message.dart';
import '../../core/utils/date_formatter.dart';

class ApiMessageModel extends ApiMessage {
  const ApiMessageModel({
    required super.id,
    required super.conversationId,
    required super.userId,
    required super.senderRole,
    required super.messageType,
    required super.content,
    required super.createdAt,
    super.referenceId,
  });

  factory ApiMessageModel.fromJson(Map<String, dynamic> json) {
    return ApiMessageModel(
      id: json['id'] as String,
      conversationId: json['conversation_id'] as String,
      userId: json['user_id'] as String,
      senderRole: SenderRoleExtension.fromString(json['sender_role'] as String),
      messageType: MessageTypeExtension.fromString(json['message_type'] as String? ?? 'text'),
      content: json['content'] as String,
      createdAt: DateFormatter.parseIsoDate(json['created_at'] as String) ?? DateTime.now(),
      referenceId: json['reference_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'user_id': userId,
      'sender_role': senderRole.value,
      'message_type': messageType.value,
      'content': content,
      'created_at': DateFormatter.toIsoString(createdAt),
      if (referenceId != null) 'reference_id': referenceId,
    };
  }

  factory ApiMessageModel.fromEntity(ApiMessage message) {
    return ApiMessageModel(
      id: message.id,
      conversationId: message.conversationId,
      userId: message.userId,
      senderRole: message.senderRole,
      messageType: message.messageType,
      content: message.content,
      createdAt: message.createdAt,
      referenceId: message.referenceId,
    );
  }

  ApiMessage toEntity() {
    return ApiMessage(
      id: id,
      conversationId: conversationId,
      userId: userId,
      senderRole: senderRole,
      messageType: messageType,
      content: content,
      createdAt: createdAt,
      referenceId: referenceId,
    );
  }
}

class ApiMessagesResponseModel extends ApiMessagesResponse {
  const ApiMessagesResponseModel({
    required super.messages,
    required super.totalCount,
    required super.limit,
    required super.offset,
  });

  factory ApiMessagesResponseModel.fromJson(Map<String, dynamic> json) {
    return ApiMessagesResponseModel(
      messages: (json['messages'] as List<dynamic>?)
              ?.map((message) => ApiMessageModel.fromJson(message as Map<String, dynamic>))
              .toList() ??
          [],
      totalCount: json['total_count'] as int? ?? 0,
      limit: json['limit'] as int? ?? 50,
      offset: json['offset'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'messages': messages
          .map((message) => (message as ApiMessageModel).toJson())
          .toList(),
      'total_count': totalCount,
      'limit': limit,
      'offset': offset,
    };
  }

  factory ApiMessagesResponseModel.fromEntity(ApiMessagesResponse response) {
    return ApiMessagesResponseModel(
      messages: response.messages
          .map((message) => ApiMessageModel.fromEntity(message))
          .toList(),
      totalCount: response.totalCount,
      limit: response.limit,
      offset: response.offset,
    );
  }

  ApiMessagesResponse toEntity() {
    return ApiMessagesResponse(
      messages: messages,
      totalCount: totalCount,
      limit: limit,
      offset: offset,
    );
  }
}

class SendMessageResponseModel extends SendMessageResponse {
  const SendMessageResponseModel({
    required super.userMessage,
    required super.aiMessage,
  });

  factory SendMessageResponseModel.fromJson(Map<String, dynamic> json) {
    return SendMessageResponseModel(
      userMessage: ApiMessageModel.fromJson(json['user_message'] as Map<String, dynamic>),
      aiMessage: ApiMessageModel.fromJson(json['ai_message'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_message': (userMessage as ApiMessageModel).toJson(),
      'ai_message': (aiMessage as ApiMessageModel).toJson(),
    };
  }

  factory SendMessageResponseModel.fromEntity(SendMessageResponse response) {
    return SendMessageResponseModel(
      userMessage: ApiMessageModel.fromEntity(response.userMessage),
      aiMessage: ApiMessageModel.fromEntity(response.aiMessage),
    );
  }

  SendMessageResponse toEntity() {
    return SendMessageResponse(
      userMessage: userMessage,
      aiMessage: aiMessage,
    );
  }
}
