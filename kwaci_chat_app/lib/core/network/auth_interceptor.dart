import 'package:dio/dio.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Authentication interceptor that automatically adds Bearer tokens to API requests
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      // Get the current session from Supabase
      final session = Supabase.instance.client.auth.currentSession;
      
      if (session != null && session.accessToken.isNotEmpty) {
        // Add Bearer token to Authorization header
        options.headers['Authorization'] = 'Bearer ${session.accessToken}';
      }
    } catch (e) {
      // If there's an error getting the token, continue without it
      print('⚠️ Failed to get auth token: $e');
    }
    
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle 401 Unauthorized responses
    if (err.response?.statusCode == 401) {
      print('🔒 Unauthorized request - token may be expired');
      // You could trigger a token refresh here if needed
    }
    
    super.onError(err, handler);
  }
}
