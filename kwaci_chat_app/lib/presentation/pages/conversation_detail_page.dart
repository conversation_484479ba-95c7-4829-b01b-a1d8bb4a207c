import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/conversation.dart';
import '../../domain/entities/api_message.dart';
import '../providers/conversation_detail_providers.dart';
import '../providers/document_providers.dart';
import '../widgets/api_message_bubble.dart';
import '../widgets/conversation_message_input.dart';
import '../widgets/typing_indicator.dart';

class ConversationDetailPage extends ConsumerStatefulWidget {
  final Conversation? conversation; // null for new conversation

  const ConversationDetailPage({super.key, this.conversation});

  @override
  ConsumerState<ConversationDetailPage> createState() =>
      _ConversationDetailPageState();
}

class _ConversationDetailPageState
    extends ConsumerState<ConversationDetailPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final List<String> _selectedDocuments = [];
  late final String? _conversationId;
  bool _isNewConversation = false;

  @override
  void initState() {
    super.initState();
    _conversationId = widget.conversation?.id;
    _isNewConversation = widget.conversation == null;

    // Load conversation and documents when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isNewConversation && _conversationId != null) {
        ref
            .read(conversationDetailProvider(_conversationId).notifier)
            .loadConversation(widget.conversation!);
      }
      ref.read(documentListProvider.notifier).loadDocuments();
    });

    // Setup scroll controller for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Load more messages when scrolled to top
      if (!_isNewConversation && _conversationId != null) {
        ref
            .read(conversationDetailProvider(_conversationId).notifier)
            .loadMoreMessages();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final newConversationState =
        _isNewConversation ? ref.watch(newConversationProvider) : null;

    // Handle navigation after new conversation is created
    if (_isNewConversation &&
        newConversationState?.createdConversation != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final conversation = newConversationState!.createdConversation!;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder:
                (context) => ConversationDetailPage(conversation: conversation),
          ),
        );
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle()),
        elevation: 0,
        actions: [
          if (!_isNewConversation && widget.conversation != null)
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'refresh':
                    _refreshConversation();
                    break;
                }
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'refresh',
                      child: Row(
                        children: [
                          Icon(Icons.refresh),
                          SizedBox(width: 8),
                          Text('Refresh'),
                        ],
                      ),
                    ),
                  ],
            ),
        ],
      ),
      body: Column(
        children: [
          // Messages List
          Expanded(child: _buildMessagesList()),

          // Typing Indicator
          if (_isSending())
            const Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: TypingIndicator(),
            ),

          // Message Input
          ConversationMessageInput(
            controller: _messageController,
            selectedDocuments: _selectedDocuments,
            onSend: _sendMessage,
            onDocumentSelected: _onDocumentSelected,
            onDocumentRemoved: _onDocumentRemoved,
            isEnabled: !_isSending(),
          ),
        ],
      ),
    );
  }

  String _getTitle() {
    if (_isNewConversation) {
      return 'New Conversation';
    }
    return widget.conversation?.displayTitle ?? 'Conversation';
  }

  bool _isSending() {
    if (_isNewConversation) {
      return ref.watch(newConversationProvider).isSending;
    }
    return ref.watch(conversationDetailProvider(_conversationId)).isSending;
  }

  Widget _buildMessagesList() {
    if (_isNewConversation) {
      return _buildNewConversationView();
    }

    final state = ref.watch(conversationDetailProvider(_conversationId));

    if (state.isLoading && state.messages.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null && state.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Error loading messages',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: _refreshConversation,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No messages yet',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Send a message to start the conversation.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      reverse: true, // Show newest messages at bottom
      itemCount: state.messages.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.messages.length) {
          // Load more indicator at top
          if (state.isLoadingMore) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: Center(child: CircularProgressIndicator()),
            );
          }
          return const SizedBox.shrink();
        }

        final message = state.messages[state.messages.length - 1 - index];
        return ApiMessageBubble(
          message: message,
          onCopy: () => _copyMessage(message.content),
        );
      },
    );
  }

  Widget _buildNewConversationView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Start a new conversation',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Send your first message to begin chatting with the AI assistant.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    // Clear the input
    _messageController.clear();

    if (_isNewConversation) {
      // Create new conversation with first message
      await ref
          .read(newConversationProvider.notifier)
          .sendFirstMessage(
            content: content,
            title: null, // Let API auto-generate title
          );
    } else {
      // Send message to existing conversation
      final referenceId =
          _selectedDocuments.isNotEmpty ? _selectedDocuments.first : null;
      await ref
          .read(conversationDetailProvider(_conversationId).notifier)
          .sendMessage(
            content: content,
            messageType:
                referenceId != null ? MessageType.document : MessageType.text,
            referenceId: referenceId,
          );
    }

    // Clear selected documents after sending
    setState(() {
      _selectedDocuments.clear();
    });

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0, // Scroll to bottom (reverse list)
          duration: AppConstants.shortAnimation,
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _onDocumentSelected(String documentId) {
    setState(() {
      if (!_selectedDocuments.contains(documentId)) {
        _selectedDocuments.add(documentId);
      }
    });
  }

  void _onDocumentRemoved(String documentId) {
    setState(() {
      _selectedDocuments.remove(documentId);
    });
  }

  void _refreshConversation() {
    if (!_isNewConversation && _conversationId != null) {
      ref
          .read(conversationDetailProvider(_conversationId).notifier)
          .loadConversation(widget.conversation!);
    }
  }

  void _copyMessage(String content) {
    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
