import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/conversation.dart';
import '../../domain/usecases/get_conversations.dart';
import '../../data/repositories/chat_repository_impl.dart';

// Conversation list state
class ConversationListState {
  final List<Conversation> conversations;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final bool hasMore;
  final int currentOffset;

  const ConversationListState({
    this.conversations = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.hasMore = true,
    this.currentOffset = 0,
  });

  ConversationListState copyWith({
    List<Conversation>? conversations,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    bool? hasMore,
    int? currentOffset,
  }) {
    return ConversationListState(
      conversations: conversations ?? this.conversations,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentOffset: currentOffset ?? this.currentOffset,
    );
  }
}

// Conversation list notifier
class ConversationListNotifier extends StateNotifier<ConversationListState> {
  final GetConversations _getConversations;
  static const int _pageSize = 50;

  ConversationListNotifier({
    required GetConversations getConversations,
  }) : _getConversations = getConversations,
       super(const ConversationListState());

  Future<void> loadConversations({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentOffset: 0,
        hasMore: true,
      );
    } else if (state.isLoading || state.isLoadingMore || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true, error: null);
    }

    final offset = refresh ? 0 : state.currentOffset;
    final result = await _getConversations(
      limit: _pageSize,
      offset: offset,
    );

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          isLoadingMore: false,
          error: failure.message,
        );
      },
      (response) {
        final newConversations = refresh 
            ? response.conversations
            : [...state.conversations, ...response.conversations];
        
        state = state.copyWith(
          conversations: newConversations,
          isLoading: false,
          isLoadingMore: false,
          error: null,
          hasMore: response.hasMore,
          currentOffset: offset + response.conversations.length,
        );
      },
    );
  }

  Future<void> loadMoreConversations() async {
    if (!state.hasMore || state.isLoadingMore || state.isLoading) {
      return;
    }
    await loadConversations(refresh: false);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Use case provider
final getConversationsProvider = Provider<GetConversations>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return GetConversations(repository);
});

// Conversation list provider
final conversationListProvider = StateNotifierProvider<ConversationListNotifier, ConversationListState>((ref) {
  final getConversations = ref.read(getConversationsProvider);
  return ConversationListNotifier(getConversations: getConversations);
});

// Convenience providers
final isLoadingConversationsProvider = Provider<bool>((ref) {
  final state = ref.watch(conversationListProvider);
  return state.isLoading;
});

final conversationsErrorProvider = Provider<String?>((ref) {
  final state = ref.watch(conversationListProvider);
  return state.error;
});

final hasMoreConversationsProvider = Provider<bool>((ref) {
  final state = ref.watch(conversationListProvider);
  return state.hasMore;
});
