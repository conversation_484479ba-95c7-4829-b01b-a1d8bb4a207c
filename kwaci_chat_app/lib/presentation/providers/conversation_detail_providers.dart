import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/api_message.dart';
import '../../domain/entities/conversation.dart';
import '../../domain/usecases/get_conversation_messages.dart';
import '../../domain/usecases/create_conversation.dart';
import '../../domain/usecases/send_conversation_message.dart';
import '../../data/repositories/chat_repository_impl.dart';

// Use cases providers
final getConversationMessagesProvider = Provider<GetConversationMessages>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return GetConversationMessages(repository);
});

final createConversationProvider = Provider<CreateConversation>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return CreateConversation(repository);
});

final sendConversationMessageProvider = Provider<SendConversationMessage>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return SendConversationMessage(repository);
});

// State classes
class ConversationDetailState {
  final Conversation? conversation;
  final List<ApiMessage> messages;
  final bool isLoading;
  final bool isLoadingMore;
  final bool isSending;
  final String? error;
  final bool hasMore;
  final int currentOffset;

  const ConversationDetailState({
    this.conversation,
    this.messages = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isSending = false,
    this.error,
    this.hasMore = true,
    this.currentOffset = 0,
  });

  ConversationDetailState copyWith({
    Conversation? conversation,
    List<ApiMessage>? messages,
    bool? isLoading,
    bool? isLoadingMore,
    bool? isSending,
    String? error,
    bool? hasMore,
    int? currentOffset,
  }) {
    return ConversationDetailState(
      conversation: conversation ?? this.conversation,
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isSending: isSending ?? this.isSending,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentOffset: currentOffset ?? this.currentOffset,
    );
  }
}

class NewConversationState {
  final List<ApiMessage> messages;
  final bool isSending;
  final String? error;
  final Conversation? createdConversation;

  const NewConversationState({
    this.messages = const [],
    this.isSending = false,
    this.error,
    this.createdConversation,
  });

  NewConversationState copyWith({
    List<ApiMessage>? messages,
    bool? isSending,
    String? error,
    Conversation? createdConversation,
  }) {
    return NewConversationState(
      messages: messages ?? this.messages,
      isSending: isSending ?? this.isSending,
      error: error,
      createdConversation: createdConversation ?? this.createdConversation,
    );
  }
}

// Conversation detail notifier
class ConversationDetailNotifier extends StateNotifier<ConversationDetailState> {
  final GetConversationMessages _getMessages;
  final SendConversationMessage _sendMessage;

  ConversationDetailNotifier(this._getMessages, this._sendMessage)
      : super(const ConversationDetailState());

  Future<void> loadConversation(Conversation conversation) async {
    state = state.copyWith(
      conversation: conversation,
      isLoading: true,
      error: null,
      messages: [],
      currentOffset: 0,
      hasMore: true,
    );

    await _loadMessages();
  }

  Future<void> _loadMessages() async {
    if (state.conversation == null) return;

    final result = await _getMessages(
      conversationId: state.conversation!.id,
      limit: 50,
      offset: state.currentOffset,
    );

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          isLoadingMore: false,
          error: failure.message,
        );
      },
      (response) {
        final newMessages = state.currentOffset == 0
            ? response.messages
            : [...state.messages, ...response.messages];

        state = state.copyWith(
          messages: newMessages,
          isLoading: false,
          isLoadingMore: false,
          hasMore: response.hasMore,
          currentOffset: state.currentOffset + response.messages.length,
          error: null,
        );
      },
    );
  }

  Future<void> loadMoreMessages() async {
    if (state.isLoadingMore || !state.hasMore || state.conversation == null) {
      return;
    }

    state = state.copyWith(isLoadingMore: true);
    await _loadMessages();
  }

  Future<void> sendMessage({
    required String content,
    MessageType messageType = MessageType.text,
    String? referenceId,
  }) async {
    if (state.conversation == null || state.isSending) return;

    state = state.copyWith(isSending: true, error: null);

    final result = await _sendMessage(
      conversationId: state.conversation!.id,
      content: content,
      messageType: messageType,
      referenceId: referenceId,
    );

    result.fold(
      (failure) {
        state = state.copyWith(
          isSending: false,
          error: failure.message,
        );
      },
      (response) {
        final updatedMessages = [
          ...state.messages,
          response.userMessage,
          response.aiMessage,
        ];

        state = state.copyWith(
          messages: updatedMessages,
          isSending: false,
          error: null,
        );
      },
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// New conversation notifier
class NewConversationNotifier extends StateNotifier<NewConversationState> {
  final CreateConversation _createConversation;

  NewConversationNotifier(this._createConversation)
      : super(const NewConversationState());

  Future<void> sendFirstMessage({
    required String content,
    String? title,
  }) async {
    if (state.isSending) return;

    state = state.copyWith(isSending: true, error: null);

    final result = await _createConversation(
      title: title,
      initialMessage: content,
    );

    result.fold(
      (failure) {
        state = state.copyWith(
          isSending: false,
          error: failure.message,
        );
      },
      (conversation) {
        state = state.copyWith(
          isSending: false,
          createdConversation: conversation,
          error: null,
        );
      },
    );
  }

  void reset() {
    state = const NewConversationState();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final conversationDetailProvider = StateNotifierProvider.family<
    ConversationDetailNotifier, ConversationDetailState, String?>((ref, conversationId) {
  final getMessages = ref.read(getConversationMessagesProvider);
  final sendMessage = ref.read(sendConversationMessageProvider);
  return ConversationDetailNotifier(getMessages, sendMessage);
});

final newConversationProvider = StateNotifierProvider<NewConversationNotifier, NewConversationState>((ref) {
  final createConversation = ref.read(createConversationProvider);
  return NewConversationNotifier(createConversation);
});
