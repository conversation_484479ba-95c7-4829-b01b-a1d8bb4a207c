import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/date_formatter.dart';
import '../../domain/entities/api_message.dart';

class ApiMessageBubble extends StatelessWidget {
  final ApiMessage message;
  final VoidCallback? onCopy;

  const ApiMessageBubble({super.key, required this.message, this.onCopy});

  @override
  Widget build(BuildContext context) {
    final isUser = message.isUser;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            _buildAvatar(context, isUser),
            const SizedBox(width: AppConstants.smallPadding),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context, isUser),
                const SizedBox(height: 4),
                _buildTimestamp(context, isUser),
              ],
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: AppConstants.smallPadding),
            _buildAvatar(context, isUser),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, bool isUser) {
    return CircleAvatar(
      radius: 16,
      backgroundColor:
          isUser
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.secondary,
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy,
        size: 16,
        color:
            isUser
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSecondary,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, bool isUser) {
    final theme = Theme.of(context);

    return GestureDetector(
      onLongPress: () => _showMessageOptions(context),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        decoration: BoxDecoration(
          color:
              isUser
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(
            AppConstants.borderRadius,
          ).copyWith(
            bottomLeft: isUser ? null : const Radius.circular(4),
            bottomRight: isUser ? const Radius.circular(4) : null,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (message.referenceId != null) ...[
              _buildDocumentReference(context, isUser),
              const SizedBox(height: AppConstants.smallPadding),
            ],
            Text(
              message.content,
              style: theme.textTheme.bodyMedium?.copyWith(
                color:
                    isUser
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentReference(BuildContext context, bool isUser) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color:
            isUser
                ? theme.colorScheme.onPrimary.withValues(alpha: 0.1)
                : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.attach_file,
            size: 16,
            color:
                isUser
                    ? theme.colorScheme.onPrimary.withValues(alpha: 0.7)
                    : theme.colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            'Document attached',
            style: theme.textTheme.bodySmall?.copyWith(
              color:
                  isUser
                      ? theme.colorScheme.onPrimary.withValues(alpha: 0.7)
                      : theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context, bool isUser) {
    return Text(
      DateFormatter.formatChatTime(message.createdAt),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
      ),
    );
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.copy),
                  title: const Text('Copy message'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _copyToClipboard(context);
                  },
                ),
                if (message.referenceId != null)
                  ListTile(
                    leading: const Icon(Icons.attach_file),
                    title: const Text('View document'),
                    onTap: () {
                      Navigator.of(context).pop();
                      // TODO: Implement document viewing
                    },
                  ),
              ],
            ),
          ),
    );
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
    onCopy?.call();
  }
}
