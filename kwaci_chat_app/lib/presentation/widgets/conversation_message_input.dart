import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../providers/document_providers.dart';

class ConversationMessageInput extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final List<String> selectedDocuments;
  final Function(String) onSend;
  final Function(String) onDocumentSelected;
  final Function(String) onDocumentRemoved;
  final bool isEnabled;

  const ConversationMessageInput({
    super.key,
    required this.controller,
    required this.selectedDocuments,
    required this.onSend,
    required this.onDocumentSelected,
    required this.onDocumentRemoved,
    this.isEnabled = true,
  });

  @override
  ConsumerState<ConversationMessageInput> createState() =>
      _ConversationMessageInputState();
}

class _ConversationMessageInputState
    extends ConsumerState<ConversationMessageInput> {
  final FocusNode _focusNode = FocusNode();
  bool _isComposing = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _isComposing = widget.controller.text.trim().isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Selected documents
            if (widget.selectedDocuments.isNotEmpty) _buildSelectedDocuments(),

            // Input area
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  // Document attachment button
                  IconButton(
                    onPressed: widget.isEnabled ? _showDocumentPicker : null,
                    icon: Icon(
                      Icons.attach_file,
                      color:
                          widget.selectedDocuments.isNotEmpty
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),

                  // Text input
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius * 2,
                        ),
                      ),
                      child: TextField(
                        controller: widget.controller,
                        focusNode: _focusNode,
                        enabled: widget.isEnabled,
                        maxLines: null,
                        textCapitalization: TextCapitalization.sentences,
                        decoration: InputDecoration(
                          hintText: 'Type a message...',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                            vertical: AppConstants.smallPadding,
                          ),
                        ),
                        onSubmitted:
                            _isComposing && widget.isEnabled
                                ? _handleSubmit
                                : null,
                      ),
                    ),
                  ),

                  const SizedBox(width: AppConstants.smallPadding),

                  // Send button
                  IconButton(
                    onPressed:
                        _isComposing && widget.isEnabled ? _handleSend : null,
                    icon: Icon(
                      Icons.send,
                      color:
                          _isComposing && widget.isEnabled
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.4),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedDocuments() {
    final documentState = ref.watch(documentListProvider);

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Documents:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: AppConstants.smallPadding,
            runSpacing: AppConstants.smallPadding,
            children:
                widget.selectedDocuments.map((documentId) {
                  final document =
                      documentState.documents
                          .where((doc) => doc.id == documentId)
                          .firstOrNull;

                  return Chip(
                    label: Text(
                      document?.filename ?? 'Unknown Document',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () => widget.onDocumentRemoved(documentId),
                    backgroundColor:
                        Theme.of(context).colorScheme.primaryContainer,
                    deleteIconColor:
                        Theme.of(context).colorScheme.onPrimaryContainer,
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  void _showDocumentPicker() {
    final documentState = ref.read(documentListProvider);

    if (documentState.documents.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'No documents available. Please upload documents first.',
          ),
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.6,
            maxChildSize: 0.9,
            minChildSize: 0.3,
            builder:
                (context, scrollController) => Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(AppConstants.borderRadius),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Handle
                      Container(
                        margin: const EdgeInsets.symmetric(
                          vertical: AppConstants.smallPadding,
                        ),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),

                      // Header
                      Padding(
                        padding: const EdgeInsets.all(
                          AppConstants.defaultPadding,
                        ),
                        child: Row(
                          children: [
                            Text(
                              'Select Documents',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const Spacer(),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Done'),
                            ),
                          ],
                        ),
                      ),

                      // Document list
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                          ),
                          itemCount: documentState.documents.length,
                          itemBuilder: (context, index) {
                            final document = documentState.documents[index];
                            final isSelected = widget.selectedDocuments
                                .contains(document.id);

                            return CheckboxListTile(
                              title: Text(document.filename),
                              subtitle: Text(
                                '${_formatFileSize(document.metadata.fileSize)} • ${document.metadata.contentType}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              value: isSelected,
                              onChanged: (selected) {
                                if (selected == true) {
                                  widget.onDocumentSelected(document.id);
                                } else {
                                  widget.onDocumentRemoved(document.id);
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  void _handleSubmit(String text) {
    if (text.trim().isNotEmpty) {
      _handleSend();
    }
  }

  void _handleSend() {
    final text = widget.controller.text.trim();
    if (text.isNotEmpty) {
      widget.onSend(text);
      _focusNode.requestFocus();
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
